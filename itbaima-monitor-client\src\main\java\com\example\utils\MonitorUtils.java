package com.example.utils;

import com.example.entity.BaseDetail;
import com.example.entity.ConnectionConfig;
import com.example.entity.RuntimeDetail;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.HWDiskStore;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;
import oshi.software.os.OperatingSystem;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 系统监控数据收集工具类
 *
 * 该工具类负责收集系统的各项监控数据，包括：
 * 1. 基础系统信息：操作系统、CPU、内存、磁盘、网络等硬件信息
 * 2. 运行时数据：CPU使用率、内存使用量、磁盘IO、网络IO等动态数据
 *
 * 技术实现：
 * - 使用OSHI库获取系统硬件和运行时信息
 * - 支持跨平台运行（Windows、Linux、macOS）
 * - 通过时间差计算动态数据（如CPU使用率、网络流量等）
 *
 * 数据收集项：
 * - 系统信息：操作系统类型、版本、架构等
 * - CPU信息：型号、核心数、使用率等
 * - 内存信息：总容量、使用量等
 * - 磁盘信息：总容量、使用量、读写速度等
 * - 网络信息：IP地址、上传下载速度等
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class MonitorUtils {

    /**
     * 连接配置，包含网络接口等配置信息
     * 使用@Lazy延迟加载，避免循环依赖
     */
    @Lazy
    @Resource
    ConnectionConfig config;

    /**
     * OSHI系统信息对象，用于获取系统硬件和软件信息
     */
    private final SystemInfo info = new SystemInfo();

    /**
     * 系统属性对象，用于获取JVM和操作系统相关属性
     */
    private final Properties properties = System.getProperties();

    /**
     * 收集系统基础信息
     *
     * 该方法收集系统的静态基础信息，这些信息通常不会频繁变化
     * 主要用于客户端首次注册时向服务端报告系统配置
     *
     * 收集的信息包括：
     * - 操作系统：架构、名称、版本、位数
     * - CPU：型号、核心数
     * - 内存：总容量（GB）
     * - 磁盘：总容量（GB）
     * - 网络：IP地址
     *
     * @return 包含系统基础信息的BaseDetail对象
     */
    public BaseDetail monitorBaseDetail() {
        // 获取操作系统信息
        OperatingSystem os = info.getOperatingSystem();
        // 获取硬件抽象层
        HardwareAbstractionLayer hardware = info.getHardware();

        // 计算内存总容量（转换为GB）
        double memory = hardware.getMemory().getTotal() / 1024.0 / 1024 /1024;

        // 计算磁盘总容量（转换为GB）
        double diskSize = Arrays.stream(File.listRoots())
                .mapToLong(File::getTotalSpace).sum() / 1024.0 / 1024 / 1024;

        // 获取网络接口的IP地址
        String ip = Objects.requireNonNull(this.findNetworkInterface(hardware)).getIPv4addr()[0];

        // 构建并返回基础详情对象
        return new BaseDetail()
                .setOsArch(properties.getProperty("os.arch"))  // 操作系统架构
                .setOsName(os.getFamily())  // 操作系统名称
                .setOsVersion(os.getVersionInfo().getVersion())  // 操作系统版本
                .setOsBit(os.getBitness())  // 操作系统位数
                .setCpuName(hardware.getProcessor().getProcessorIdentifier().getName())  // CPU型号
                .setCpuCore(hardware.getProcessor().getLogicalProcessorCount())  // CPU核心数
                .setMemory(memory)  // 内存总容量
                .setDisk(diskSize)  // 磁盘总容量
                .setIp(ip);  // IP地址
    }

    public RuntimeDetail monitorRuntimeDetail() {
        double statisticTime = 0.5;
        try {
            HardwareAbstractionLayer hardware = info.getHardware();
            NetworkIF networkInterface = Objects.requireNonNull(this.findNetworkInterface(hardware));
            CentralProcessor processor = hardware.getProcessor();
            double upload = networkInterface.getBytesSent(), download = networkInterface.getBytesRecv();
            double read = hardware.getDiskStores().stream().mapToLong(HWDiskStore::getReadBytes).sum();
            double write = hardware.getDiskStores().stream().mapToLong(HWDiskStore::getWriteBytes).sum();
            long[] ticks = processor.getSystemCpuLoadTicks();
            Thread.sleep((long) (statisticTime * 1000));
            networkInterface = Objects.requireNonNull(this.findNetworkInterface(hardware));
            upload = (networkInterface.getBytesSent() - upload) / statisticTime;
            download =  (networkInterface.getBytesRecv() - download) / statisticTime;
            read = (hardware.getDiskStores().stream().mapToLong(HWDiskStore::getReadBytes).sum() - read) / statisticTime;
            write = (hardware.getDiskStores().stream().mapToLong(HWDiskStore::getWriteBytes).sum() - write) / statisticTime;
            double memory = (hardware.getMemory().getTotal() - hardware.getMemory().getAvailable()) / 1024.0 / 1024 / 1024;
            double disk = Arrays.stream(File.listRoots())
                    .mapToLong(file -> file.getTotalSpace() - file.getFreeSpace()).sum() / 1024.0 / 1024 / 1024;
            return new RuntimeDetail()
                    .setCpuUsage(this.calculateCpuUsage(processor, ticks))
                    .setMemoryUsage(memory)
                    .setDiskUsage(disk)
                    .setNetworkUpload(upload / 1024)
                    .setNetworkDownload(download / 1024)
                    .setDiskRead(read / 1024/ 1024)
                    .setDiskWrite(write / 1024 / 1024)
                    .setTimestamp(new Date().getTime());
        } catch (Exception e) {
            log.error("读取运行时数据出现问题", e);
        }
        return null;
    }

    private double calculateCpuUsage(CentralProcessor processor, long[] prevTicks) {
        long[] ticks = processor.getSystemCpuLoadTicks();
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()]
                - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()]
                - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softIrq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()]
                - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()]
                - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];
        long cSys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()]
                - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long cUser = ticks[CentralProcessor.TickType.USER.getIndex()]
                - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long ioWait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()]
                - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()]
                - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long totalCpu = cUser + nice + cSys + idle + ioWait + irq + softIrq + steal;
        return (cSys + cUser) * 1.0 / totalCpu;
    }

    public List<String> listNetworkInterfaceName() {
        HardwareAbstractionLayer hardware = info.getHardware();
        return hardware.getNetworkIFs()
                .stream()
                .map(NetworkIF::getName)
                .toList();
    }

    private NetworkIF findNetworkInterface(HardwareAbstractionLayer hardware) {
        try {
            String target = config.getNetworkInterface();
            List<NetworkIF> ifs = hardware.getNetworkIFs()
                    .stream()
                    .filter(inter -> inter.getName().equals(target))
                    .toList();
            if (!ifs.isEmpty()) {
                return ifs.get(0);
            } else {
                throw new IOException("网卡信息错误，找不到网卡: " + target);
            }
        } catch (IOException e) {
            log.error("读取网络接口信息时出错", e);
        }
        return null;
    }
}
