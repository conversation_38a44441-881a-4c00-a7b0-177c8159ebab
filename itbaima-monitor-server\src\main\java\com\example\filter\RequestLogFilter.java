package com.example.filter;

import com.alibaba.fastjson2.JSONObject;
import com.example.utils.Const;
import com.example.utils.SnowflakeIdGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.Set;

/**
 * 请求日志过滤器，用于记录所有用户请求信息
 *
 * 该过滤器继承自OncePerRequestFilter，确保每个请求只被过滤一次
 * 主要功能：
 * 1. 记录请求开始时的详细信息（URL、参数、用户身份等）
 * 2. 记录请求结束时的响应信息和处理耗时
 * 3. 为每个请求生成唯一的请求ID用于日志追踪
 * 4. 过滤掉不需要记录日志的特定URL路径
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class RequestLogFilter extends OncePerRequestFilter {

    /**
     * 雪花算法ID生成器，用于为每个请求生成唯一的请求ID
     */
    @Resource
    SnowflakeIdGenerator generator;

    /**
     * 需要忽略日志记录的URL路径集合
     * 包含：
     * - /swagger-ui: Swagger文档界面
     * - /v3/api-docs: API文档接口
     * - /monitor/runtime: 运行时监控接口
     * - /api/monitor/list: 监控列表接口
     * - /api/monitor/runtime-now: 实时监控接口
     */
    private final Set<String> ignores = Set.of("/swagger-ui", "/v3/api-docs", "/monitor/runtime",
            "/api/monitor/list", "/api/monitor/runtime-now");

    /**
     * 过滤器核心方法，处理每个HTTP请求
     *
     * 处理流程：
     * 1. 检查当前请求URL是否在忽略列表中
     * 2. 如果需要忽略，直接放行到下一个过滤器
     * 3. 如果需要记录日志，则：
     *    - 记录请求开始时间
     *    - 记录请求开始日志
     *    - 使用ContentCachingResponseWrapper包装响应以便读取响应内容
     *    - 执行后续过滤器链
     *    - 记录请求结束日志（包含耗时和响应结果）
     *    - 将缓存的响应内容写回给客户端
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param filterChain 过滤器链
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 检查是否为需要忽略日志记录的URL
        if(this.isIgnoreUrl(request.getServletPath())) {
            // 直接放行，不记录日志
            filterChain.doFilter(request, response);
        } else {
            // 记录请求开始时间，用于计算处理耗时
            long startTime = System.currentTimeMillis();

            // 记录请求开始日志
            this.logRequestStart(request);

            // 使用ContentCachingResponseWrapper包装响应，以便能够读取响应内容
            ContentCachingResponseWrapper wrapper = new ContentCachingResponseWrapper(response);

            // 继续执行过滤器链
            filterChain.doFilter(request, wrapper);

            // 记录请求结束日志
            this.logRequestEnd(wrapper, startTime);

            // 将缓存的响应内容复制回原始响应对象，确保客户端能正常接收响应
            wrapper.copyBodyToResponse();
        }
    }

    /**
     * 判定当前请求URL是否不需要日志打印
     *
     * 遍历忽略列表，检查请求URL是否以忽略路径开头
     * 主要用于过滤掉系统内部接口、文档接口等不需要记录的请求
     *
     * @param url 请求路径
     * @return true-需要忽略日志记录，false-需要记录日志
     */
    private boolean isIgnoreUrl(String url){
        // 遍历所有需要忽略的URL前缀
        for (String ignore : ignores) {
            // 如果当前请求URL以忽略前缀开头，则返回true
            if(url.startsWith(ignore)) return true;
        }
        // 如果没有匹配到任何忽略前缀，则需要记录日志
        return false;
    }

    /**
     * 请求结束时的日志打印，包含处理耗时以及响应结果
     *
     * 计算请求处理耗时，获取响应状态码和响应内容
     * 如果响应状态码不是200，则记录错误信息
     * 如果响应状态码是200，则记录完整的响应内容
     *
     * @param wrapper 用于读取响应结果的包装类，包含了缓存的响应内容
     * @param startTime 请求开始处理的时间戳（毫秒）
     */
    public void logRequestEnd(ContentCachingResponseWrapper wrapper, long startTime){
        // 计算请求处理耗时
        long time = System.currentTimeMillis() - startTime;

        // 获取HTTP响应状态码
        int status = wrapper.getStatus();

        // 根据状态码决定记录的内容
        String content = status != 200 ?
                status + " 错误" : new String(wrapper.getContentAsByteArray());

        // 记录请求处理耗时和响应结果
        log.info("请求处理耗时: {}ms | 响应结果: {}", time, content);
    }

    /**
     * 请求开始时的日志打印，包含请求全部信息，以及对应用户角色
     *
     * 主要功能：
     * 1. 生成唯一的请求ID并设置到MDC中，用于日志追踪
     * 2. 提取并格式化请求参数
     * 3. 获取当前用户身份信息（如果已认证）
     * 4. 记录详细的请求日志，包括URL、方法、IP、用户信息、参数等
     *
     * @param request HTTP请求对象，包含所有请求信息
     */
    public void logRequestStart(HttpServletRequest request){
        // 生成唯一的请求ID，用于日志追踪
        long reqId = generator.nextId();
        // 将请求ID设置到MDC（Mapped Diagnostic Context）中，后续日志都会包含这个ID
        MDC.put("reqId", String.valueOf(reqId));

        // 创建JSON对象来存储请求参数
        JSONObject object = new JSONObject();
        // 遍历所有请求参数，将参数名和参数值存入JSON对象
        // 如果参数值数组长度大于0，取第一个值；否则设为null
        request.getParameterMap().forEach((k, v) -> object.put(k, v.length > 0 ? v[0] : null));

        // 尝试从请求属性中获取用户ID
        Object id = request.getAttribute(Const.ATTR_USER_ID);

        if(id != null) {
            // 如果用户已认证，获取用户详细信息
            User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            // 记录包含用户身份信息的详细日志
            log.info("请求URL: \"{}\" ({}) | 远程IP地址: {} │ 身份: {} (UID: {}) | 角色: {} | 请求参数列表: {}",
                    request.getServletPath(), request.getMethod(), request.getRemoteAddr(),
                    user.getUsername(), id, user.getAuthorities(), object);
        } else {
            // 如果用户未认证，记录基本的请求信息
            log.info("请求URL: \"{}\" ({}) | 远程IP地址: {} │ 身份: 未验证 | 请求参数列表: {}",
                    request.getServletPath(), request.getMethod(), request.getRemoteAddr(), object);
        }
    }
}
