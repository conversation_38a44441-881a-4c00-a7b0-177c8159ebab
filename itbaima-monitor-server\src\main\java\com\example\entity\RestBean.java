package com.example.entity;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import org.slf4j.MDC;

import java.util.Optional;

/**
 * REST API统一响应实体类
 *
 * 该类使用Java 14的Record特性，提供统一的API响应格式
 * 所有的REST接口都应该使用此类来包装响应数据
 *
 * 响应格式：
 * {
 *   "id": 请求ID（用于日志追踪）,
 *   "code": HTTP状态码,
 *   "data": 响应数据（泛型类型）,
 *   "message": 响应消息
 * }
 *
 * 主要功能：
 * 1. 统一API响应格式
 * 2. 提供常用响应状态的快捷方法
 * 3. 集成请求ID用于日志追踪
 * 4. 支持JSON序列化
 *
 * 使用场景：
 * - 成功响应：success()
 * - 失败响应：failure()
 * - 权限相关：forbidden(), unauthorized(), noPermission()
 *
 * @param id 请求ID，从MDC中获取，用于日志追踪
 * @param code HTTP状态码（200成功，4xx客户端错误，5xx服务器错误）
 * @param data 响应数据，泛型类型，可以是任何对象
 * @param message 响应消息，用于描述请求结果
 * @param <T> 响应数据的泛型类型
 *
 * <AUTHOR>
 * @version 1.0
 */
public record RestBean<T> (long id, int code, T data, String message) {

    /**
     * 创建成功响应（带数据）
     *
     * @param data 要返回的数据
     * @param <T> 数据类型
     * @return 成功响应实体
     */
    public static <T> RestBean<T> success(T data){
        return new RestBean<>(requestId(), 200, data, "请求成功");
    }

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应实体
     */
    public static <T> RestBean<T> success(){
        return success(null);
    }

    /**
     * 创建403禁止访问响应
     *
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 禁止访问响应实体
     */
    public static <T> RestBean<T> forbidden(String message){
        return failure(403, message);
    }

    /**
     * 创建401未授权响应
     *
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 未授权响应实体
     */
    public static <T> RestBean<T> unauthorized(String message){
        return failure(401, message);
    }

    /**
     * 创建失败响应
     *
     * @param code HTTP状态码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应实体
     */
    public static <T> RestBean<T> failure(int code, String message){
        return new RestBean<>(requestId(), code, null, message);
    }

    /**
     * 创建权限不足响应
     *
     * 用于用户已认证但权限不足的场景
     *
     * @param <T> 数据类型
     * @return 权限不足响应实体
     */
    public static <T> RestBean<T> noPermission() {
        return new RestBean<>(requestId(), 401, null, "权限不足，拒绝访问");
    }

    /**
     * 将当前响应实体转换为JSON字符串
     *
     * 使用FastJSON库进行序列化，配置WriteNulls特性确保null值也被序列化
     * 主要用于在过滤器和异常处理器中直接写入响应
     *
     * @return JSON格式的响应字符串
     */
    public String asJsonString() {
        return JSONObject.toJSONString(this, JSONWriter.Feature.WriteNulls);
    }

    /**
     * 获取当前请求的唯一ID
     *
     * 从MDC（Mapped Diagnostic Context）中获取请求ID
     * 该ID由RequestLogFilter在请求开始时设置，用于日志追踪
     * 如果获取不到则返回0作为默认值
     *
     * 作用：
     * - 将请求ID包含在响应中，方便前后端日志关联
     * - 便于问题排查和请求追踪
     * - 支持分布式系统的链路追踪
     *
     * @return 当前请求的唯一ID，如果获取不到则返回0
     */
    private static long requestId(){
        String requestId = Optional.ofNullable(MDC.get("reqId")).orElse("0");
        return Long.parseLong(requestId);
    }
}
