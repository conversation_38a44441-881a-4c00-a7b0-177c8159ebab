{"name": "my-project-frontend", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.3.0", "axios": "^1.4.0", "echarts": "^5.4.3", "element-plus": "^2.3.9", "flag-icon-css": "^4.1.7", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "less": "^4.2.0", "unplugin-auto-import": "^0.15.2", "unplugin-vue-components": "^0.24.1", "vite": "^4.4.6"}}