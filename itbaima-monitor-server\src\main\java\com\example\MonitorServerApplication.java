package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 监控系统服务端主启动类
 *
 * 这是一个基于Spring Boot的监控系统服务端应用程序
 * 主要功能：
 * 1. 提供监控数据的收集和存储服务
 * 2. 提供用户认证和授权功能
 * 3. 提供RESTful API接口供前端调用
 * 4. 支持WebSocket实时通信
 * 5. 集成消息队列处理异步任务
 * 6. 支持SSH终端连接功能
 *
 * 技术栈：
 * - Spring Boot 3.x
 * - Spring Security（安全认证）
 * - MyBatis（数据访问）
 * - JWT（令牌认证）
 * - RabbitMQ（消息队列）
 * - InfluxDB（时序数据库）
 * - WebSocket（实时通信）
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
@SpringBootApplication
public class MonitorServerApplication {

    /**
     * 应用程序主入口方法
     *
     * 启动Spring Boot应用程序，初始化所有组件和服务
     * 包括：
     * - Web服务器（默认Tomcat）
     * - 数据库连接池
     * - 安全配置
     * - 消息队列连接
     * - WebSocket服务
     *
     * @param args 命令行参数，可用于传递配置参数
     */
    public static void main(String[] args) {
        SpringApplication.run(MonitorServerApplication.class, args);
    }

}
