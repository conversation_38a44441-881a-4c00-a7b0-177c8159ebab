/*
 监控系统数据库初始化脚本

 该SQL脚本用于创建监控系统所需的数据库表结构和初始数据

 数据库信息：
 - 数据库名称：monitor
 - 数据库类型：MySQL 8.2.0
 - 字符编码：UTF-8 (utf8mb4)

 表结构说明：
 1. db_account - 用户账户表
    - 存储系统用户的基本信息和权限
    - 支持管理员和普通用户角色
    - 包含用户可访问的客户端列表

 2. db_client - 监控客户端表
    - 存储注册的监控客户端信息
    - 包含客户端基本配置和状态
    - 支持客户端令牌认证

 3. db_client_detail - 客户端详情表
    - 存储客户端的详细硬件信息
    - 包含CPU、内存、磁盘等配置信息
    - 用于系统信息展示和分析

 4. db_client_ssh - 客户端SSH配置表
    - 存储SSH连接配置信息
    - 支持密码和密钥认证方式
    - 用于远程终端连接功能

 使用说明：
 1. 在MySQL中创建名为"monitor"的数据库
 2. 执行此脚本创建表结构
 3. 脚本会自动创建默认管理员账户
 4. 配置应用程序连接到此数据库

 安全注意事项：
 - 默认管理员密码需要在生产环境中修改
 - 建议为应用程序创建专用数据库用户
 - 定期备份数据库数据

 Navicat Premium Data Transfer
 Source Server         : 本地数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : localhost:3306
 Source Schema         : monitor
 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001
 Date: 14/12/2023 18:44:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for db_account
-- ----------------------------
DROP TABLE IF EXISTS `db_account`;
CREATE TABLE `db_account` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `role` varchar(255) DEFAULT NULL,
  `clients` json DEFAULT NULL,
  `register_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  UNIQUE KEY `unique_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for db_client
-- ----------------------------
DROP TABLE IF EXISTS `db_client`;
CREATE TABLE `db_client` (
  `id` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `token` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `node` varchar(255) DEFAULT NULL,
  `register_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for db_client_detail
-- ----------------------------
DROP TABLE IF EXISTS `db_client_detail`;
CREATE TABLE `db_client_detail` (
  `id` int NOT NULL,
  `os_arch` varchar(255) DEFAULT NULL,
  `os_name` varchar(255) DEFAULT NULL,
  `os_version` varchar(255) DEFAULT NULL,
  `os_bit` int DEFAULT NULL,
  `cpu_name` varchar(255) DEFAULT NULL,
  `cpu_core` int DEFAULT NULL,
  `memory` double DEFAULT NULL,
  `disk` double DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for db_client_ssh
-- ----------------------------
DROP TABLE IF EXISTS `db_client_ssh`;
CREATE TABLE `db_client_ssh` (
  `id` int NOT NULL,
  `port` int DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

SET FOREIGN_KEY_CHECKS = 1;
