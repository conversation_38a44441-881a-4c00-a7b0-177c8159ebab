package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 监控系统客户端主启动类
 *
 * 这是一个基于Spring Boot的监控客户端应用程序
 * 主要功能：
 * 1. 收集本机的系统运行时数据（CPU、内存、磁盘、网络等）
 * 2. 定期向监控服务端发送监控数据
 * 3. 接收服务端的配置和控制指令
 * 4. 提供系统基础信息的采集和上报
 *
 * 技术特性：
 * - 基于Spring Boot框架
 * - 使用Quartz定时任务调度
 * - 支持HTTP通信协议
 * - 轻量级部署，资源占用少
 *
 * 部署方式：
 * - 可以作为独立应用程序运行
 * - 支持多平台部署（Windows、Linux、macOS）
 * - 通过注册令牌连接到监控服务端
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
@SpringBootApplication
public class MonitorClientApplication {

	/**
	 * 客户端应用程序主入口方法
	 *
	 * 启动监控客户端应用程序，初始化以下组件：
	 * - 定时任务调度器（Quartz）
	 * - 系统监控数据收集器
	 * - 网络通信组件
	 * - 配置管理组件
	 *
	 * @param args 命令行参数，可用于传递配置参数
	 */
	public static void main(String[] args) {
		SpringApplication.run(MonitorClientApplication.class, args);
	}

}
