package com.example.websocket;

import com.example.entity.dto.ClientDetail;
import com.example.entity.dto.ClientSsh;
import com.example.mapper.ClientDetailMapper;
import com.example.mapper.ClientSshMapper;
import com.jcraft.jsch.ChannelShell;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import jakarta.annotation.Resource;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 终端WebSocket服务端点
 *
 * 该类提供基于WebSocket的SSH终端功能，允许用户通过Web界面
 * 远程连接到监控客户端所在的服务器进行命令行操作
 *
 * 主要功能：
 * 1. WebSocket连接管理：处理客户端连接和断开
 * 2. SSH连接建立：根据配置连接到目标服务器
 * 3. 终端交互：实现Web终端与SSH会话的双向通信
 * 4. 会话管理：维护WebSocket会话与SSH会话的映射关系
 *
 * 技术实现：
 * - 使用JSch库建立SSH连接
 * - WebSocket实现实时双向通信
 * - 多线程处理SSH输出流读取
 * - 并发安全的会话管理
 *
 * 安全特性：
 * - 基于客户端ID的权限控制
 * - SSH密钥或密码认证
 * - 会话隔离和自动清理
 *
 * 使用场景：
 * - 远程服务器管理
 * - 实时命令执行
 * - 系统维护和调试
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
@ServerEndpoint("/terminal/{clientId}")
public class TerminalWebSocket {

    /**
     * 客户端详情数据访问对象（静态注入）
     * 用于获取客户端的基础信息
     */
    private static ClientDetailMapper detailMapper;

    /**
     * 设置客户端详情Mapper（Spring依赖注入）
     * 由于WebSocket端点不是Spring管理的Bean，需要通过静态方法注入
     */
    @Resource
    public void setDetailMapper(ClientDetailMapper detailMapper) {
        TerminalWebSocket.detailMapper = detailMapper;
    }

    /**
     * 客户端SSH配置数据访问对象（静态注入）
     * 用于获取SSH连接配置信息
     */
    private static ClientSshMapper sshMapper;

    /**
     * 设置SSH配置Mapper（Spring依赖注入）
     */
    @Resource
    public void setSshMapper(ClientSshMapper sshMapper) {
        TerminalWebSocket.sshMapper = sshMapper;
    }

    /**
     * WebSocket会话与SSH Shell的映射关系
     * 使用ConcurrentHashMap确保线程安全
     */
    private static final Map<Session, Shell> sessionMap = new ConcurrentHashMap<>();

    /**
     * 单线程执行器，用于处理SSH输出流的读取
     * 避免多线程竞争和资源冲突
     */
    private final ExecutorService service = Executors.newSingleThreadExecutor();

    @OnOpen
    public void onOpen(Session session,
                        @PathParam(value = "clientId") String clientId) throws Exception {
        ClientDetail detail = detailMapper.selectById(clientId);
        ClientSsh ssh = sshMapper.selectById(clientId);
        if(detail == null || ssh == null) {
            session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "无法识别此主机"));
            return;
        }
        if(this.createSshConnection(session, ssh, detail.getIp())) {
            log.info("主机 {} 的SSH连接已创建", detail.getIp());
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) throws IOException {
        Shell shell = sessionMap.get(session);
        OutputStream output = shell.output;
        output.write(message.getBytes(StandardCharsets.UTF_8));
        output.flush();
    }

    @OnClose
    public void onClose(Session session) throws IOException {
        Shell shell = sessionMap.get(session);
        if(shell != null) {
            shell.close();
            sessionMap.remove(session);
            log.info("主机 {} 的SSH连接已断开", shell.js.getHost());
        }
    }

    @OnError
    public void onError(Session session, Throwable error) throws IOException {
        log.error("用户WebSocket连接出现错误", error);
        session.close();
    }

    private boolean createSshConnection(Session session, ClientSsh ssh, String ip) throws IOException{
        try {
            JSch jSch = new JSch();
            com.jcraft.jsch.Session js = jSch.getSession(ssh.getUsername(), ip, ssh.getPort());
            js.setPassword(ssh.getPassword());
            js.setConfig("StrictHostKeyChecking", "no");
            js.setTimeout(3000);
            js.connect();
            ChannelShell channel = (ChannelShell) js.openChannel("shell");
            channel.setPtyType("xterm");
            channel.connect(1000);
            sessionMap.put(session, new Shell(session, js, channel));
            return true;
        } catch (JSchException e) {
            String message = e.getMessage();
            if(message.equals("Auth fail")) {
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT,
                        "登录SSH失败，用户名或密码错误"));
                log.error("连接SSH失败，用户名或密码错误，登录失败");
            } else if(message.contains("Connection refused")) {
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT,
                        "连接被拒绝，可能是没有启动SSH服务或是放开端口"));
                log.error("连接SSH失败，连接被拒绝，可能是没有启动SSH服务或是放开端口");
            } else {
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, message));
                log.error("连接SSH时出现错误", e);
            }
        }
        return false;
    }

    private class Shell {
        private final Session session;
        private final com.jcraft.jsch.Session js;
        private final ChannelShell channel;
        private final InputStream input;
        private final OutputStream output;

        public Shell(Session session, com.jcraft.jsch.Session js, ChannelShell channel) throws IOException {
            this.js = js;
            this.session = session;
            this.channel = channel;
            this.input = channel.getInputStream();
            this.output = channel.getOutputStream();
            service.submit(this::read);
        }

        private void read() {
            try {
                byte[] buffer = new byte[1024 * 1024];
                int i;
                while ((i = input.read(buffer)) != -1) {
                    String text = new String(Arrays.copyOfRange(buffer, 0, i), StandardCharsets.UTF_8);
                    session.getBasicRemote().sendText(text);
                }
            } catch (Exception e) {
                log.error("读取SSH输入流时出现问题", e);
            }
        }

        public void close() throws IOException {
            input.close();
            output.close();
            channel.disconnect();
            js.disconnect();
            service.shutdown();
        }
    }
}
