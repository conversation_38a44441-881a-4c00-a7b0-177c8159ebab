/**
 * 监控系统前端应用程序主入口文件
 *
 * 该文件负责初始化Vue 3应用程序和相关的插件、配置
 *
 * 主要功能：
 * 1. 创建Vue应用实例
 * 2. 配置路由系统
 * 3. 配置状态管理（Pinia）
 * 4. 配置HTTP请求库（Axios）
 * 5. 引入样式文件和图标库
 * 6. 挂载应用到DOM
 *
 * 技术栈：
 * - Vue 3：前端框架
 * - Vue Router：路由管理
 * - Pinia：状态管理
 * - Axios：HTTP请求库
 * - Element Plus：UI组件库
 * - ECharts：图表库
 *
 * <AUTHOR>
 * @version 1.0
 */

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import axios from "axios";

// 引入样式文件
import '@/assets/css/element.less'  // Element Plus自定义样式
import 'flag-icon-css/css/flag-icons.min.css'  // 国旗图标样式
import 'element-plus/theme-chalk/dark/css-vars.css'  // Element Plus暗色主题

// 引入状态管理相关
import {createPinia} from "pinia";
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// 配置Axios默认基础URL，指向后端服务器
axios.defaults.baseURL = 'http://localhost:8080'

// 创建Vue应用实例
const app = createApp(App)

// 创建并配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)
// 使用持久化插件，自动保存状态到localStorage
pinia.use(piniaPluginPersistedstate)

// 使用Vue Router路由系统
app.use(router)

// 将应用挂载到DOM元素上
app.mount('#app')
