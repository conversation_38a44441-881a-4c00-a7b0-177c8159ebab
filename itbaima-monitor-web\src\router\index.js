/**
 * Vue Router路由配置
 *
 * 该文件配置了前端应用的所有路由规则和导航守卫
 *
 * 路由结构：
 * 1. 欢迎页面路由（/）：
 *    - 登录页面（/）
 *    - 忘记密码页面（/forget）
 *
 * 2. 主应用路由（/index）：
 *    - 监控管理页面（/index）
 *    - 安全设置页面（/index/security）
 *
 * 导航守卫：
 * - 未认证用户只能访问欢迎页面
 * - 已认证用户自动跳转到主应用
 * - 防止未授权访问受保护的页面
 *
 * 技术特性：
 * - 使用HTML5 History模式
 * - 懒加载组件，提高首屏加载速度
 * - 嵌套路由支持
 * - 全局导航守卫
 *
 * <AUTHOR>
 * @version 1.0
 */

import { createRouter, createWebHistory } from 'vue-router'
import { unauthorized } from "@/net";

/**
 * 创建路由实例
 */
const router = createRouter({
    // 使用HTML5 History模式，URL更美观且SEO友好
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            // 欢迎页面路由（未认证用户访问）
            path: '/',
            name: 'welcome',
            component: () => import('@/views/WelcomeView.vue'),
            children: [
                {
                    // 登录页面（默认页面）
                    path: '',
                    name: 'welcome-login',
                    component: () => import('@/views/welcome/LoginPage.vue')
                }, {
                    // 忘记密码页面
                    path: 'forget',
                    name: 'welcome-forget',
                    component: () => import('@/views/welcome/ForgetPage.vue')
                }
            ]
        }, {
            // 主应用路由（已认证用户访问）
            path: '/index',
            name: 'index',
            component: () => import('@/views/IndexView.vue'),
            children: [
                {
                    // 监控管理页面（默认页面）
                    path: '',
                    name: 'manage',
                    component: () => import('@/views/main/Manage.vue')
                }, {
                    // 安全设置页面
                    path: 'security',
                    name: 'security',
                    component: () => import('@/views/main/Security.vue')
                }
            ]
        }
    ]
})

/**
 * 全局前置导航守卫
 *
 * 在每次路由跳转前执行，用于控制页面访问权限
 *
 * 权限控制逻辑：
 * 1. 如果用户已认证但访问欢迎页面，自动跳转到主应用
 * 2. 如果用户未认证但访问主应用页面，自动跳转到登录页
 * 3. 其他情况正常放行
 *
 * 这样确保了：
 * - 已登录用户不会看到登录页面
 * - 未登录用户无法访问受保护的页面
 * - 提供良好的用户体验和安全性
 *
 * @param {Object} to 即将进入的目标路由对象
 * @param {Object} from 当前导航正要离开的路由对象
 * @param {Function} next 进行管道中的下一个钩子
 */
router.beforeEach((to, from, next) => {
    // 检查用户是否未认证（没有有效的JWT令牌）
    const isUnauthorized = unauthorized()

    if(to.name.startsWith('welcome') && !isUnauthorized) {
        // 用户已认证但访问欢迎页面，重定向到主应用
        next('/index')
    } else if(to.fullPath.startsWith('/index') && isUnauthorized) {
        // 用户未认证但访问主应用页面，重定向到登录页
        next('/')
    } else {
        // 其他情况正常放行
        next()
    }
})

export default router
