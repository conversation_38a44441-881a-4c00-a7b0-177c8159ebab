package com.example.utils;

/**
 * 系统常量定义类
 *
 * 该类集中定义了系统中使用的所有常量字符串和数值
 * 主要用于避免硬编码，提高代码的可维护性和一致性
 *
 * 常量分类：
 * 1. JWT令牌相关：黑名单、频率限制等Redis键前缀
 * 2. 用户管理相关：用户黑名单等Redis键前缀
 * 3. 流量控制相关：请求频率限制的Redis键前缀
 * 4. 邮件验证相关：验证码限制和数据的Redis键前缀
 * 5. 过滤器优先级：定义各个过滤器的执行顺序
 * 6. 请求属性名：在请求中传递的自定义属性名称
 * 7. 消息队列相关：队列名称定义
 * 8. 用户角色相关：系统中的用户角色定义
 *
 * 使用规范：
 * - 所有常量都使用public static final修饰
 * - Redis键使用冒号分隔的命名空间格式
 * - 常量名使用大写字母和下划线
 *
 * <AUTHOR>
 * @version 1.0
 */
public final class Const {

    // ==================== JWT令牌相关常量 ====================
    /**
     * JWT令牌黑名单Redis键前缀
     * 用于存储已失效的JWT令牌ID，防止令牌重复使用
     */
    public final static String JWT_BLACK_LIST = "jwt:blacklist:";

    /**
     * JWT令牌申请频率限制Redis键前缀
     * 用于限制用户申请JWT令牌的频率，防止恶意刷令牌
     */
    public final static String JWT_FREQUENCY = "jwt:frequency:";

    // ==================== 用户管理相关常量 ====================
    /**
     * 用户黑名单Redis键前缀
     * 用于存储被封禁的用户ID
     */
    public final static String USER_BLACK_LIST = "user:blacklist:";

    // ==================== 流量控制相关常量 ====================
    /**
     * 流量限制计数器Redis键前缀
     * 用于记录用户的请求次数
     */
    public final static String FLOW_LIMIT_COUNTER = "flow:counter:";

    /**
     * 流量限制封禁Redis键前缀
     * 用于存储被临时封禁的用户或IP
     */
    public final static String FLOW_LIMIT_BLOCK = "flow:block:";

    // ==================== 邮件验证相关常量 ====================
    /**
     * 邮件验证码发送频率限制Redis键前缀
     * 用于限制邮件验证码的发送频率
     */
    public final static String VERIFY_EMAIL_LIMIT = "verify:email:limit:";

    /**
     * 邮件验证码数据Redis键前缀
     * 用于存储邮件验证码的内容和过期时间
     */
    public final static String VERIFY_EMAIL_DATA = "verify:email:data:";

    // ==================== 过滤器优先级常量 ====================
    /**
     * 流量限制过滤器优先级
     * 数值越小优先级越高，-101表示较高优先级
     */
    public final static int ORDER_FLOW_LIMIT = -101;

    /**
     * CORS跨域过滤器优先级
     * -102表示最高优先级，确保CORS处理在其他过滤器之前
     */
    public final static int ORDER_CORS = -102;

    // ==================== 请求属性名常量 ====================
    /**
     * 请求中用户ID属性名
     * 用于在过滤器中传递当前用户的ID
     */
    public final static String ATTR_USER_ID = "userId";

    /**
     * 请求中用户角色属性名
     * 用于在过滤器中传递当前用户的角色信息
     */
    public final static String ATTR_USER_ROLE = "userRole";

    /**
     * 请求中客户端信息属性名
     * 用于在监控相关请求中传递客户端信息
     */
    public final static String ATTR_CLIENT = "client";

    // ==================== 消息队列相关常量 ====================
    /**
     * 邮件发送消息队列名称
     * 用于异步处理邮件发送任务
     */
    public final static String MQ_MAIL = "mail";

    // ==================== 用户角色相关常量 ====================
    /**
     * 管理员角色标识
     * 拥有系统的所有权限
     */
    public final static String ROLE_ADMIN = "admin";

    /**
     * 普通用户角色标识
     * 只能访问分配给自己的资源
     */
    public final static String ROLE_NORMAL = "user";
}
