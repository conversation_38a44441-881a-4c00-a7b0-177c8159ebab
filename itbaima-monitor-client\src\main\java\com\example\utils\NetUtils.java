package com.example.utils;

import com.alibaba.fastjson2.JSONObject;
import com.example.entity.BaseDetail;
import com.example.entity.ConnectionConfig;
import com.example.entity.Response;
import com.example.entity.RuntimeDetail;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * 网络通信工具类
 *
 * 该工具类负责客户端与服务端之间的所有HTTP通信，包括：
 * 1. 客户端注册：向服务端注册客户端信息
 * 2. 基础信息上报：发送系统基础配置信息
 * 3. 运行时数据上报：定期发送系统运行时监控数据
 * 4. HTTP请求封装：统一处理GET和POST请求
 *
 * 技术实现：
 * - 使用Java 11的HttpClient进行HTTP通信
 * - 支持JSON数据格式的请求和响应
 * - 统一的错误处理和日志记录
 * - 自动添加认证头信息
 *
 * 通信协议：
 * - 使用HTTP/HTTPS协议
 * - JSON格式数据传输
 * - Bearer Token认证
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class NetUtils {

    /**
     * HTTP客户端实例，用于发送HTTP请求
     * 使用Java 11的新HttpClient，支持HTTP/2
     */
    private final HttpClient client = HttpClient.newHttpClient();

    /**
     * 连接配置，包含服务端地址和认证令牌
     * 使用@Lazy延迟加载，避免循环依赖
     */
    @Lazy
    @Resource
    ConnectionConfig config;

    /**
     * 向服务端注册客户端
     *
     * 客户端启动时调用此方法向服务端注册自己
     * 注册成功后，服务端会记录客户端信息，客户端才能正常上报数据
     *
     * @param address 服务端地址（如：http://localhost:8080）
     * @param token 注册令牌，由服务端管理员生成
     * @return true-注册成功，false-注册失败
     */
    public boolean registerToServer(String address, String token) {
        log.info("正在向服务端注册，请稍后...");
        Response response = this.doGet("/register", address, token);
        if(response.success()) {
            log.info("客户端注册已完成！");
        } else {
            log.error("客户端注册失败: {}", response.message());
        }
        return response.success();
    }

    /**
     * 发送GET请求（使用配置中的地址和令牌）
     *
     * @param url 请求路径
     * @return 服务端响应对象
     */
    private Response doGet(String url) {
        return this.doGet(url, config.getAddress(), config.getToken());
    }

    /**
     * 发送GET请求到服务端
     *
     * @param url 请求路径
     * @param address 服务端地址
     * @param token 认证令牌
     * @return 服务端响应对象
     */
    private Response doGet(String url, String address, String token) {
        try {
            // 构建HTTP GET请求
            HttpRequest request = HttpRequest.newBuilder().GET()
                    .uri(new URI(address + "/monitor" + url))
                    .header("Authorization", token)
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 解析JSON响应为Response对象
            return JSONObject.parseObject(response.body()).to(Response.class);
        } catch (Exception e) {
            log.error("在发起服务端请求时出现问题", e);
            return Response.errorResponse(e);
        }
    }

    /**
     * 更新系统基础信息到服务端
     *
     * 将客户端的基础系统信息（如CPU、内存、磁盘等硬件信息）发送给服务端
     * 通常在客户端启动时或系统配置发生变化时调用
     *
     * @param detail 包含系统基础信息的对象
     */
    public void updateBaseDetails(BaseDetail detail) {
        Response response = this.doPost("/detail", detail);
        if(response.success()) {
            log.info("系统基本信息已更新完成");
        } else {
            log.error("系统基本信息更新失败: {}", response.message());
        }
    }

    /**
     * 更新运行时监控数据到服务端
     *
     * 将客户端的实时运行数据（如CPU使用率、内存使用量等）发送给服务端
     * 由定时任务定期调用，用于实时监控
     *
     * @param detail 包含运行时监控数据的对象
     */
    public void updateRuntimeDetails(RuntimeDetail detail) {
        Response response = this.doPost("/runtime", detail);
        if(!response.success()) {
            log.warn("更新运行时状态时，接收到服务端的异常响应内容: {}", response.message());
        }
    }

    /**
     * 发送POST请求到服务端
     *
     * 将数据对象序列化为JSON格式，并发送POST请求到指定的服务端接口
     *
     * @param url 请求路径
     * @param data 要发送的数据对象
     * @return 服务端响应对象
     */
    private Response doPost(String url, Object data) {
        try {
            // 将数据对象序列化为JSON字符串
            String rawData = JSONObject.from(data).toJSONString();

            // 构建HTTP POST请求
            HttpRequest request = HttpRequest.newBuilder().POST(HttpRequest.BodyPublishers.ofString(rawData))
                    .uri(new URI(config.getAddress() + "/monitor" + url))
                    .header("Authorization", config.getToken())  // 添加认证头
                    .header("Content-Type", "application/json")  // 设置内容类型
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 解析JSON响应为Response对象
            return JSONObject.parseObject(response.body()).to(Response.class);
        } catch (Exception e) {
            log.error("在发起服务端请求时出现问题", e);
            return Response.errorResponse(e);
        }
    }
}
