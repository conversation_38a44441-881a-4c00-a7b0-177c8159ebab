package com.example.entity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运行时监控数据实体类
 *
 * 该类封装了系统运行时的动态监控数据，用于记录系统的实时性能指标
 * 这些数据会被定期收集并发送给监控服务端进行存储和分析
 *
 * 数据字段说明：
 * - timestamp：数据采集时间戳（毫秒）
 * - cpuUsage：CPU使用率（0.0-1.0，表示百分比）
 * - memoryUsage：内存使用量（GB）
 * - diskUsage：磁盘使用量（GB）
 * - networkUpload：网络上传速度（KB/s）
 * - networkDownload：网络下载速度（KB/s）
 * - diskRead：磁盘读取速度（MB/s）
 * - diskWrite：磁盘写入速度（MB/s）
 *
 * 使用场景：
 * - 系统性能监控
 * - 资源使用情况分析
 * - 性能趋势统计
 * - 异常检测和告警
 *
 * 数据来源：
 * - 通过OSHI库从操作系统获取
 * - 定时任务每10秒收集一次
 * - 实时反映系统当前状态
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
public class RuntimeDetail {
    /**
     * 数据采集时间戳（毫秒）
     * 记录数据采集的具体时间，用于时序数据分析
     */
    long timestamp;

    /**
     * CPU使用率（0.0-1.0）
     * 表示CPU的使用百分比，1.0表示100%使用率
     */
    double cpuUsage;

    /**
     * 内存使用量（GB）
     * 当前已使用的内存大小，单位为GB
     */
    double memoryUsage;

    /**
     * 磁盘使用量（GB）
     * 当前已使用的磁盘空间大小，单位为GB
     */
    double diskUsage;

    /**
     * 网络上传速度（KB/s）
     * 当前网络上传的速度，单位为KB/秒
     */
    double networkUpload;

    /**
     * 网络下载速度（KB/s）
     * 当前网络下载的速度，单位为KB/秒
     */
    double networkDownload;

    /**
     * 磁盘读取速度（MB/s）
     * 当前磁盘读取的速度，单位为MB/秒
     */
    double diskRead;

    /**
     * 磁盘写入速度（MB/s）
     * 当前磁盘写入的速度，单位为MB/秒
     */
    double diskWrite;
}
