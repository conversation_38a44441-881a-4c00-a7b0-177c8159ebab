/**
 * Pinia状态管理存储
 *
 * 该文件定义了应用程序的全局状态管理，使用Pinia作为状态管理库
 *
 * 主要功能：
 * 1. 用户信息管理：存储当前登录用户的基本信息
 * 2. 权限判断：提供用户角色相关的计算属性
 * 3. 状态持久化：自动保存状态到本地存储
 *
 * 状态结构：
 * - user.role：用户角色（admin/user）
 * - user.username：用户名
 * - user.email：用户邮箱
 *
 * 计算属性：
 * - isAdmin：判断当前用户是否为管理员
 *
 * 持久化：
 * - 使用pinia-plugin-persistedstate插件
 * - 自动保存状态到localStorage
 * - 页面刷新后状态不丢失
 *
 * 使用方式：
 * ```javascript
 * import { useStore } from '@/store'
 * const store = useStore()
 * console.log(store.user.username)
 * console.log(store.isAdmin)
 * ```
 *
 * <AUTHOR>
 * @version 1.0
 */

import {defineStore} from "pinia";

/**
 * 定义全局状态存储
 */
export const useStore = defineStore('general', {
    /**
     * 状态定义
     * 返回应用程序的初始状态
     */
    state: () => {
        return {
            // 用户信息对象
            user: {
                role: '',      // 用户角色：admin（管理员）或 user（普通用户）
                username: '',  // 用户名
                email: ''      // 用户邮箱
            }
        }
    },

    /**
     * 计算属性（Getters）
     * 基于状态计算派生数据
     */
    getters: {
        /**
         * 判断当前用户是否为管理员
         * @returns {boolean} true-管理员，false-普通用户
         */
        isAdmin() {
            return this.user.role === 'admin'
        }
    },

    /**
     * 启用状态持久化
     * 自动将状态保存到localStorage，页面刷新后恢复
     */
    persist: true
})
