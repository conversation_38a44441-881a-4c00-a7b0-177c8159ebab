package com.example.filter;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.example.entity.RestBean;
import com.example.entity.dto.Account;
import com.example.entity.dto.Client;
import com.example.service.AccountService;
import com.example.service.ClientService;
import com.example.utils.Const;
import com.example.utils.JwtUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.ArrayList;

/**
 * JWT认证过滤器
 *
 * 该过滤器负责验证HTTP请求中的JWT令牌，并为通过验证的请求设置认证信息
 * 支持两种类型的认证：
 * 1. 用户JWT令牌认证：用于Web前端用户的API请求
 * 2. 客户端令牌认证：用于监控客户端的数据上报请求
 *
 * 主要功能：
 * 1. JWT令牌解析和验证
 * 2. 用户身份信息提取和设置
 * 3. 客户端身份验证和信息设置
 * 4. Spring Security认证上下文管理
 * 5. 请求属性设置（用户ID、角色、客户端信息）
 *
 * 处理流程：
 * 1. 检查请求路径，区分用户请求和客户端请求
 * 2. 提取Authorization头中的令牌
 * 3. 验证令牌的有效性和完整性
 * 4. 设置Spring Security认证信息
 * 5. 在请求属性中存储用户/客户端信息
 *
 * 安全特性：
 * - 令牌黑名单检查
 * - 令牌过期验证
 * - 用户状态检查
 * - 客户端状态验证
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    /**
     * JWT工具类，用于令牌的解析和验证
     */
    @Resource
    JwtUtils utils;

    /**
     * 客户端服务，用于客户端令牌的验证和信息获取
     */
    @Resource
    ClientService service;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        String authorization = request.getHeader("Authorization");
        String uri = request.getRequestURI();
        if(uri.startsWith("/monitor")) {
            if(!uri.endsWith("/register")) {
                Client client = service.findClientByToken(authorization);
                if(client == null) {
                    response.setStatus(401);
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().write(RestBean.failure(401, "未注册").asJsonString());
                    return;
                } else {
                    request.setAttribute(Const.ATTR_CLIENT, client);
                }
            }
        } else {
            DecodedJWT jwt = utils.resolveJwt(authorization);
            if(jwt != null) {
                UserDetails user = utils.toUser(jwt);
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
                request.setAttribute(Const.ATTR_USER_ID, utils.toId(jwt));
                request.setAttribute(Const.ATTR_USER_ROLE, new ArrayList<>(user.getAuthorities()).get(0).getAuthority());

                if(request.getRequestURI().startsWith("/terminal/") && !accessShell(
                        (int) request.getAttribute(Const.ATTR_USER_ID),
                        (String) request.getAttribute(Const.ATTR_USER_ROLE),
                        Integer.parseInt(request.getRequestURI().substring(10)))) {
                    response.setStatus(401);
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().write(RestBean.failure(401, "无权访问").asJsonString());
                    return;
                }
            }
        }
        filterChain.doFilter(request, response);
    }

    @Resource
    AccountService accountService;

    private boolean accessShell(int userId, String userRole, int clientId) {
        if(Const.ROLE_ADMIN.equals(userRole.substring(5))) {
            return true;
        } else {
            Account account = accountService.getById(userId);
            return account.getClientList().contains(clientId);
        }
    }
}
