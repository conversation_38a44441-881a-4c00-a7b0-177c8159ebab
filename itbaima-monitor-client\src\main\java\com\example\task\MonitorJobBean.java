package com.example.task;

import com.example.entity.RuntimeDetail;
import com.example.utils.MonitorUtils;
import com.example.utils.NetUtils;
import jakarta.annotation.Resource;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * 监控数据收集定时任务
 *
 * 该类是监控客户端的核心定时任务，负责定期收集系统运行时数据并上报给服务端
 * 继承自QuartzJobBean，由Quartz调度器定期执行
 *
 * 主要功能：
 * 1. 收集系统运行时监控数据（CPU、内存、磁盘、网络等）
 * 2. 将收集到的数据发送给监控服务端
 * 3. 处理数据收集和发送过程中的异常
 *
 * 执行频率：
 * - 由Quartz配置决定，通常为每分钟或每几分钟执行一次
 * - 可以通过配置文件调整执行频率
 *
 * 数据流程：
 * 系统数据收集 -> 数据封装 -> 网络发送 -> 服务端存储
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class MonitorJobBean extends QuartzJobBean {

    /**
     * 监控数据收集工具，负责收集系统各项运行时数据
     */
    @Resource
    MonitorUtils monitor;

    /**
     * 网络通信工具，负责与服务端进行数据传输
     */
    @Resource
    NetUtils net;

    /**
     * 定时任务执行方法
     *
     * 该方法由Quartz调度器定期调用，执行监控数据的收集和上报
     *
     * 执行步骤：
     * 1. 调用MonitorUtils收集当前系统的运行时数据
     * 2. 调用NetUtils将数据发送给监控服务端
     * 3. 如果发送失败，异常会被Quartz框架处理
     *
     * @param context Quartz任务执行上下文，包含任务相关信息
     * @throws JobExecutionException 任务执行过程中发生的异常
     */
    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        // 收集系统运行时监控数据
        RuntimeDetail runtimeDetail = monitor.monitorRuntimeDetail();

        // 将监控数据发送给服务端
        net.updateRuntimeDetails(runtimeDetail);
    }
}
