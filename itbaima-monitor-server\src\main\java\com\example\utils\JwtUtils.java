package com.example.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * JWT令牌处理工具类
 *
 * 该工具类提供完整的JWT令牌管理功能，包括：
 * 1. JWT令牌的生成和签名
 * 2. JWT令牌的解析和验证
 * 3. JWT令牌的黑名单管理
 * 4. 用户频率限制和防刷机制
 * 5. 用户信息的提取和转换
 *
 * 安全特性：
 * - 使用HMAC256算法进行签名
 * - 支持令牌黑名单机制
 * - 防止高频申请令牌的阶段性封禁
 * - 支持用户级别的封禁
 *
 * 依赖组件：
 * - Redis：用于存储黑名单和频率限制数据
 * - FlowUtils：用于流量控制和频率检测
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class JwtUtils {

    /**
     * JWT令牌签名密钥
     * 从配置文件中读取，用于令牌的签名和验证
     */
    @Value("${spring.security.jwt.key}")
    private String key;

    /**
     * JWT令牌过期时间（小时）
     * 从配置文件中读取，决定令牌的有效期
     */
    @Value("${spring.security.jwt.expire}")
    private int expire;

    /**
     * JWT令牌生成基础冷却时间（秒）
     * 防止用户频繁申请令牌的基础限制时间
     */
    @Value("${spring.security.jwt.limit.base}")
    private int limit_base;

    /**
     * JWT令牌生成升级封禁时间（秒）
     * 用户恶意刷令牌时的更严厉封禁时间
     */
    @Value("${spring.security.jwt.limit.upgrade}")
    private int limit_upgrade;

    /**
     * 触发升级封禁的频率阈值
     * 在冷却时间内继续申请令牌的次数限制
     */
    @Value("${spring.security.jwt.limit.frequency}")
    private int limit_frequency;

    /**
     * Redis模板，用于黑名单和频率限制数据的存储
     */
    @Resource
    StringRedisTemplate template;

    /**
     * 流量控制工具，用于实现频率检测和限制
     */
    @Resource
    FlowUtils utils;

    /**
     * 使指定的JWT令牌失效
     *
     * 该方法将JWT令牌加入黑名单，使其立即失效
     * 主要用于用户退出登录时确保令牌安全
     *
     * 处理流程：
     * 1. 从请求头中提取JWT令牌
     * 2. 验证令牌的有效性和签名
     * 3. 将令牌ID加入Redis黑名单
     * 4. 设置黑名单过期时间为令牌剩余有效期
     *
     * @param headerToken 请求头中携带的完整令牌（包含Bearer前缀）
     * @return true-令牌失效成功，false-令牌格式错误或验证失败
     */
    public boolean invalidateJwt(String headerToken){
        // 提取纯净的JWT令牌（去除Bearer前缀）
        String token = this.convertToken(headerToken);
        // 创建HMAC256算法实例
        Algorithm algorithm = Algorithm.HMAC256(key);
        // 构建JWT验证器
        JWTVerifier jwtVerifier = JWT.require(algorithm).build();
        try {
            // 验证并解析JWT令牌
            DecodedJWT verify = jwtVerifier.verify(token);
            // 将令牌加入黑名单
            return deleteToken(verify.getId(), verify.getExpiresAt());
        } catch (JWTVerificationException e) {
            // 令牌验证失败（格式错误、签名无效等）
            return false;
        }
    }

    /**
     * 根据配置计算JWT令牌过期时间
     *
     * 基于当前时间和配置的过期小时数计算令牌的过期时间
     *
     * @return JWT令牌的过期时间
     */
    public Date expireTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, expire);
        return calendar.getTime();
    }

    /**
     * 根据用户信息生成JWT令牌
     *
     * 该方法是JWT令牌生成的核心方法，包含以下功能：
     * 1. 频率检测：防止用户恶意频繁申请令牌
     * 2. 令牌构建：包含用户ID、用户名、权限等信息
     * 3. 签名生成：使用HMAC256算法进行签名
     *
     * 令牌包含的声明（Claims）：
     * - jti：令牌唯一ID（用于黑名单管理）
     * - id：用户ID
     * - name：用户名
     * - authorities：用户权限列表
     * - exp：过期时间
     * - iat：签发时间
     *
     * @param user Spring Security用户详情对象
     * @param username 用户名
     * @param userId 用户ID
     * @return JWT令牌字符串，如果频率检测失败则返回null
     */
    public String createJwt(UserDetails user, String username, int userId) {
        // 进行频率检测，防止恶意刷令牌
        if(this.frequencyCheck(userId)) {
            // 创建HMAC256签名算法
            Algorithm algorithm = Algorithm.HMAC256(key);
            // 计算令牌过期时间
            Date expire = this.expireTime();
            // 构建并签名JWT令牌
            return JWT.create()
                    .withJWTId(UUID.randomUUID().toString())  // 设置令牌唯一ID
                    .withClaim("id", userId)  // 设置用户ID
                    .withClaim("name", username)  // 设置用户名
                    .withClaim("authorities", user.getAuthorities()  // 设置用户权限
                            .stream()
                            .map(GrantedAuthority::getAuthority).toList())
                    .withExpiresAt(expire)  // 设置过期时间
                    .withIssuedAt(new Date())  // 设置签发时间
                    .sign(algorithm);  // 使用算法签名
        } else {
            // 频率检测失败，拒绝生成令牌
            return null;
        }
    }

    /**
     * 解析和验证JWT令牌
     *
     * 该方法是JWT令牌验证的核心方法，执行完整的令牌验证流程：
     * 1. 提取令牌：从请求头中提取纯净的JWT令牌
     * 2. 签名验证：验证令牌的签名是否有效
     * 3. 黑名单检查：检查令牌是否在黑名单中
     * 4. 用户状态检查：检查用户是否被封禁
     * 5. 过期时间检查：验证令牌是否已过期
     *
     * 安全检查项：
     * - 令牌格式和签名验证
     * - 令牌黑名单验证
     * - 用户黑名单验证
     * - 令牌过期时间验证
     *
     * @param headerToken 请求头中携带的完整令牌（包含Bearer前缀）
     * @return 解析成功的JWT对象，验证失败返回null
     */
    public DecodedJWT resolveJwt(String headerToken){
        // 提取纯净的JWT令牌
        String token = this.convertToken(headerToken);
        if(token == null) return null;

        // 创建签名验证算法
        Algorithm algorithm = Algorithm.HMAC256(key);
        JWTVerifier jwtVerifier = JWT.require(algorithm).build();

        try {
            // 验证令牌签名并解析
            DecodedJWT verify = jwtVerifier.verify(token);

            // 检查令牌是否在黑名单中
            if(this.isInvalidToken(verify.getId())) return null;

            // 检查用户是否被封禁
            if(this.isInvalidUser(verify.getClaim("id").asInt())) return null;

            // 获取令牌声明信息
            Map<String, Claim> claims = verify.getClaims();

            // 检查令牌是否已过期（双重验证）
            return new Date().after(claims.get("exp").asDate()) ? null : verify;
        } catch (JWTVerificationException e) {
            // 令牌验证失败（签名无效、格式错误等）
            return null;
        }
    }

    /**
     * 将jwt对象中的内容封装为UserDetails
     * @param jwt 已解析的Jwt对象
     * @return UserDetails
     */
    public UserDetails toUser(DecodedJWT jwt) {
        Map<String, Claim> claims = jwt.getClaims();
        return User
                .withUsername(claims.get("name").asString())
                .password("******")
                .authorities(claims.get("authorities").asArray(String.class))
                .build();
    }

    /**
     * 将jwt对象中的用户ID提取出来
     * @param jwt 已解析的Jwt对象
     * @return 用户ID
     */
    public Integer toId(DecodedJWT jwt) {
        Map<String, Claim> claims = jwt.getClaims();
        return claims.get("id").asInt();
    }

    /**
     * 频率检测，防止用户高频申请Jwt令牌，并且采用阶段封禁机制
     * 如果已经提示无法登录的情况下用户还在刷，那么就封禁更长时间
     * @param userId 用户ID
     * @return 是否通过频率检测
     */
    private boolean frequencyCheck(int userId){
        String key = Const.JWT_FREQUENCY + userId;
        return utils.limitOnceUpgradeCheck(key, limit_frequency, limit_base, limit_upgrade);
    }

    /**
     * 校验并转换请求头中的Token令牌
     * @param headerToken 请求头中的Token
     * @return 转换后的令牌
     */
    private String convertToken(String headerToken){
        if(headerToken == null || !headerToken.startsWith("Bearer "))
            return null;
        return headerToken.substring(7);
    }

    /**
     * 将Token列入Redis黑名单中
     * @param uuid 令牌ID
     * @param time 过期时间
     * @return 是否操作成功
     */
    private boolean deleteToken(String uuid, Date time){
        if(this.isInvalidToken(uuid))
            return false;
        Date now = new Date();
        long expire = Math.max(time.getTime() - now.getTime(), 0);
        template.opsForValue().set(Const.JWT_BLACK_LIST + uuid, "", expire, TimeUnit.MILLISECONDS);
        return true;
    }

    public void deleteUser(int uid) {
        template.opsForValue().set(Const.USER_BLACK_LIST + uid, "", expire, TimeUnit.HOURS);
    }

    private boolean isInvalidUser(int uid){
        return Boolean.TRUE.equals(template.hasKey(Const.USER_BLACK_LIST + uid));
    }

    /**
     * 验证Token是否被列入Redis黑名单
     * @param uuid 令牌ID
     * @return 是否操作成功
     */
    private boolean isInvalidToken(String uuid){
        return Boolean.TRUE.equals(template.hasKey(Const.JWT_BLACK_LIST + uuid));
    }
}
