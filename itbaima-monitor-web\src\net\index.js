/**
 * 网络请求工具模块
 *
 * 该模块封装了前端与后端API的所有网络通信功能，包括：
 * 1. HTTP请求的统一封装（GET、POST）
 * 2. JWT令牌的管理（存储、获取、验证、删除）
 * 3. 用户认证相关操作（登录、登出）
 * 4. 请求和响应的统一处理
 * 5. 错误处理和用户提示
 *
 * 主要特性：
 * - 自动添加JWT认证头
 * - 令牌过期自动检测和处理
 * - 统一的错误处理和用户提示
 * - 支持记住登录状态（localStorage vs sessionStorage）
 *
 * 使用方式：
 * - login(username, password, remember, success, failure)
 * - logout(success, failure)
 * - get(url, success, failure)
 * - post(url, data, success, failure)
 * - unauthorized() - 检查是否未认证
 *
 * <AUTHOR>
 * @version 1.0
 */

import axios from "axios";
import {ElMessage} from "element-plus";
import {useStore} from "@/store";

/**
 * 本地存储中认证信息的键名
 */
const authItemName = "authorize"

/**
 * 生成包含JWT令牌的请求头
 *
 * @returns {Object} 包含Authorization头的对象
 */
const accessHeader = () => {
    return {
        'Authorization': `Bearer ${takeAccessToken()}`
    }
}

/**
 * 默认错误处理函数
 * 处理网络错误、服务器错误等异常情况
 *
 * @param {Error} error 错误对象
 */
const defaultError = (error) => {
    console.error(error)
    ElMessage.error('发生了一些错误，请联系管理员')
}

/**
 * 默认失败处理函数
 * 处理业务逻辑失败的情况（如权限不足、参数错误等）
 *
 * @param {string} message 错误消息
 * @param {number} status HTTP状态码
 * @param {string} url 请求URL
 */
const defaultFailure = (message, status, url) => {
    console.warn(`请求地址: ${url}, 状态码: ${status}, 错误信息: ${message}`)
    ElMessage.warning(message)
}

/**
 * 获取访问令牌
 *
 * 从本地存储中获取JWT令牌，并检查是否过期
 * 优先从localStorage获取，如果没有则从sessionStorage获取
 *
 * @returns {string|null} JWT令牌字符串，如果不存在或已过期则返回null
 */
function takeAccessToken() {
    // 尝试从localStorage或sessionStorage获取认证信息
    const str = localStorage.getItem(authItemName) || sessionStorage.getItem(authItemName);
    if(!str) return null

    // 解析认证对象
    const authObj = JSON.parse(str)

    // 检查令牌是否过期
    if(new Date(authObj.expire) <= new Date()) {
        // 令牌已过期，清除存储并提示用户
        deleteAccessToken()
        ElMessage.warning("登录状态已过期，请重新登录！")
        return null
    }

    return authObj.token
}

/**
 * 存储访问令牌
 *
 * 将JWT令牌和过期时间存储到本地存储中
 * 根据remember参数决定存储位置（localStorage或sessionStorage）
 *
 * @param {boolean} remember 是否记住登录状态
 * @param {string} token JWT令牌
 * @param {string} expire 过期时间
 */
function storeAccessToken(remember, token, expire){
    const authObj = {
        token: token,
        expire: expire
    }
    const str = JSON.stringify(authObj)

    if(remember)
        // 记住登录状态，存储到localStorage（浏览器关闭后仍保留）
        localStorage.setItem(authItemName, str)
    else
        // 不记住登录状态，存储到sessionStorage（浏览器关闭后清除）
        sessionStorage.setItem(authItemName, str)
}

/**
 * 删除访问令牌
 *
 * 从localStorage和sessionStorage中清除认证信息
 * 通常在用户登出或令牌过期时调用
 */
function deleteAccessToken() {
    localStorage.removeItem(authItemName)
    sessionStorage.removeItem(authItemName)
}

function internalPost(url, data, headers, success, failure, error = defaultError){
    axios.post(url, data, { headers: headers }).then(({data}) => {
        if(data.code === 200)
            success(data.data)
        else
            failure(data.message, data.code, url)
    }).catch(err => error(err))
}

function internalGet(url, headers, success, failure, error = defaultError){
    axios.get(url, { headers: headers }).then(({data}) => {
        if(data.code === 200)
            success(data.data)
        else
            failure(data.message, data.code, url)
    }).catch(err => error(err))
}

function login(username, password, remember, success, failure = defaultFailure){
    internalPost('/api/auth/login', {
        username: username,
        password: password
    }, {
        'Content-Type': 'application/x-www-form-urlencoded'
    }, (data) => {
        storeAccessToken(remember, data.token, data.expire)
        const store = useStore()
        store.user.role = data.role
        store.user.username = data.username
        store.user.email = data.email
        ElMessage.success(`登录成功，欢迎 ${data.username} 来到我们的系统`)
        success(data)
    }, failure)
}

function post(url, data, success, failure = defaultFailure) {
    internalPost(url, data, accessHeader() , success, failure)
}

function logout(success, failure = defaultFailure){
    get('/api/auth/logout', () => {
        deleteAccessToken()
        ElMessage.success(`退出登录成功，欢迎您再次使用`)
        success()
    }, failure)
}

function get(url, success, failure = defaultFailure) {
    internalGet(url, accessHeader(), success, failure)
}

function unauthorized() {
    return !takeAccessToken()
}

export { post, get, login, logout, unauthorized }
