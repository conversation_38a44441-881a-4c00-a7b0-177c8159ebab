<template>
  <el-container class="main-container">
    <el-header class="main-header">
      <el-image style="height: 30px"
                src="https://element-plus.org/images/element-plus-logo.svg"/>
      <div class="tabs">
        <tab-item v-for="item in tabs" :name="item.name"
                  :active="item.id === tab" @click="changePage(item)"/>
        <el-switch style="margin: 0 20px"
                   v-model="dark" active-color="#424242"
                   :active-action-icon="Moon"
                   :inactive-action-icon="Sunny"/>
        <div style="text-align: right;line-height: 16px;margin-right: 10px">
          <div>
            <el-tag type="success" v-if="store.isAdmin" size="small">管理员</el-tag>
            <el-tag v-else size="small">子账户</el-tag>
            {{store.user.username}}
          </div>
          <div style="font-size: 13px;color: grey">{{store.user.email}}</div>
        </div>
        <el-dropdown>
          <el-avatar class="avatar"
                     src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"/>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="userLogout">
                <el-icon><Back/></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    <el-main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="el-fade-in-linear" mode="out-in">
          <keep-alive exclude="Security">
            <component :is="Component"/>
          </keep-alive>
        </transition>
      </router-view>
    </el-main>
  </el-container>
</template>

<!--
监控系统主界面组件

该组件是用户登录后的主界面，提供了完整的应用程序框架和导航功能

主要功能：
1. 顶部导航栏：包含Logo、标签页导航、主题切换、用户信息等
2. 用户信息显示：显示当前用户的角色、用户名、邮箱等信息
3. 主题切换：支持明暗主题切换功能
4. 标签页导航：在不同功能模块间切换
5. 用户操作：提供退出登录等用户操作
6. 内容区域：使用router-view显示子页面内容

界面布局：
- el-header：顶部导航栏，包含Logo、导航、用户信息
- el-main：主内容区域，显示路由组件

技术特性：
- 使用Vue 3 Composition API
- Element Plus UI组件库
- 路由切换动画效果
- keep-alive组件缓存（排除Security页面）
- 响应式设计

用户体验：
- 直观的用户界面
- 流畅的页面切换动画
- 清晰的用户身份标识
- 便捷的主题切换功能

<AUTHOR>
@version 1.0
-->

<script setup>
import { logout } from '@/net'
import router from "@/router";
import {Back, Moon, Sunny} from "@element-plus/icons-vue";
import {ref} from "vue";
import {useDark} from "@vueuse/core";
import TabItem from "@/component/TabItem.vue";
import {useRoute} from "vue-router";
import {useStore} from "@/store";

const store = useStore()

const route = useRoute()
const dark = ref(useDark())
const tabs = [
  {id: 1, name: '管理', route: 'manage'},
  {id: 2, name: '安全', route: 'security'}
]
const defaultIndex = () => {
  for (let tab of tabs) {
    if(route.name === tab.route)
      return tab.id
  }
  return 1
}
const tab = ref(defaultIndex())
function changePage(item) {
  tab.value = item.id
  router.push({name: item.route})
}

function userLogout() {
  logout(() => router.push("/"))
}
</script>

<style scoped>
.main-container {
  height: 100vh;
  width: 100vw;

  .main-header {
    height: 55px;
    background-color: var(--el-bg-color);
    border-bottom: solid 1px var(--el-border-color);
    display: flex;
    align-items: center;

    .tabs {
      height: 55px;
      gap: 10px;
      flex: 1px;
      display: flex;
      align-items: center;
      justify-content: right;
    }
  }

  .main-content {
    height: 100%;
    background-color: #f5f5f5;
  }
}

.dark .main-container .main-content {
  background-color: #232323;
}
</style>
